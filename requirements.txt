# Electric Demand Forecasting - Required Dependencies
# Time Series Forecasting with ARIMA/SARIMA and Holt-Winter Methods

# Core data manipulation and analysis
pandas>=1.5.0
numpy>=1.21.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0

# Machine learning and metrics
scikit-learn>=1.1.0

# Time series analysis and forecasting
statsmodels>=0.13.0
pmdarima>=2.0.0

# Statistical analysis
scipy>=1.9.0

# Optional: For enhanced performance
numba>=0.56.0
joblib>=1.1.0

# Optional: For Jupyter notebook support
# jupyter>=1.0.0
# ipykernel>=6.0.0
