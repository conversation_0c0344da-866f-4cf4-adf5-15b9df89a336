#!/usr/bin/env python3
"""
Electric Demand Forecasting with ARIMA/SARIMA and Holt-Winter Methods
3-Phase Methodology Implementation
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_percentage_error
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ElectricDemandForecaster:
    def __init__(self, data_path):
        """Initialize the forecaster with data loading and preparation."""
        self.data_path = data_path
        self.df = None
        self.train_data = None
        self.val_data = None
        self.test_data = None
        self.sarimax_model = None
        self.holt_winter_model = None
        
    def load_and_prepare_data(self):
        """Phase 1: Data preparation with datetime indexing and exogenous variable encoding."""
        print("=== PHASE 1: DATA PREPARATION ===")
        
        # Load data
        self.df = pd.read_csv(self.data_path)
        print(f"Data loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
        
        # Convert DateTime to datetime index
        self.df['DateTime'] = pd.to_datetime(self.df['DateTime'])
        self.df.set_index('DateTime', inplace=True)

        # Keep hourly frequency for detailed forecasting
        self.df = self.df.asfreq('H')
        
        # Encode exogenous variables
        # Holiday encoding (weekends already count as holidays based on data)
        self.df['IsHoliday'] = self.df['Holiday'].astype(int)
        
        # Create cyclical features for time components
        self.df['Hour_sin'] = np.sin(2 * np.pi * self.df['Hour'] / 24)
        self.df['Hour_cos'] = np.cos(2 * np.pi * self.df['Hour'] / 24)
        self.df['DayOfYear'] = self.df.index.dayofyear
        self.df['DayOfYear_sin'] = np.sin(2 * np.pi * self.df['DayOfYear'] / 365.25)
        self.df['DayOfYear_cos'] = np.cos(2 * np.pi * self.df['DayOfYear'] / 365.25)
        self.df['WeekDay_sin'] = np.sin(2 * np.pi * self.df['WeekDay'] / 7)
        self.df['WeekDay_cos'] = np.cos(2 * np.pi * self.df['WeekDay'] / 7)
        
        print("Exogenous variables encoded successfully")
        print(f"Date range: {self.df.index.min()} to {self.df.index.max()}")
        
    def exploratory_data_analysis(self):
        """Perform EDA with decomposition and stationarity testing."""
        print("\n=== EXPLORATORY DATA ANALYSIS ===")
        
        # Basic statistics
        print("\nDemand Statistics:")
        print(self.df['Demand'].describe())
        
        # Time series plot
        plt.figure(figsize=(15, 8))
        plt.subplot(2, 1, 1)
        plt.plot(self.df.index, self.df['Demand'], alpha=0.7)
        plt.title('Electric Demand Time Series')
        plt.ylabel('Demand')
        plt.grid(True)
        
        # Monthly aggregation for trend visualization
        monthly_demand = self.df['Demand'].resample('M').mean()
        plt.subplot(2, 1, 2)
        plt.plot(monthly_demand.index, monthly_demand.values, marker='o')
        plt.title('Monthly Average Demand')
        plt.ylabel('Average Demand')
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        # Seasonal decomposition
        print("\nPerforming seasonal decomposition...")
        # Use weekly data for decomposition to manage computational load
        weekly_data = self.df['Demand'].resample('W').mean()
        decomposition = seasonal_decompose(weekly_data, model='additive', period=52)
        
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        decomposition.observed.plot(ax=axes[0], title='Original')
        decomposition.trend.plot(ax=axes[1], title='Trend')
        decomposition.seasonal.plot(ax=axes[2], title='Seasonal')
        decomposition.resid.plot(ax=axes[3], title='Residual')
        plt.tight_layout()
        plt.show()
        
        # Stationarity tests
        print("\n=== STATIONARITY TESTS ===")
        self.stationarity_tests(self.df['Demand'])
        
    def stationarity_tests(self, series):
        """Perform ADF and KPSS stationarity tests."""
        # ADF Test
        adf_result = adfuller(series.dropna())
        print(f"\nAugmented Dickey-Fuller Test:")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")
        print(f"Critical Values: {adf_result[4]}")
        
        if adf_result[1] <= 0.05:
            print("Result: Series is stationary (reject null hypothesis)")
        else:
            print("Result: Series is non-stationary (fail to reject null hypothesis)")
        
        # KPSS Test
        kpss_result = kpss(series.dropna())
        print(f"\nKPSS Test:")
        print(f"KPSS Statistic: {kpss_result[0]:.6f}")
        print(f"p-value: {kpss_result[1]:.6f}")
        print(f"Critical Values: {kpss_result[3]}")
        
        if kpss_result[1] <= 0.05:
            print("Result: Series is non-stationary (reject null hypothesis)")
        else:
            print("Result: Series is stationary (fail to reject null hypothesis)")
    
    def create_train_val_test_split(self):
        """Create 60/20/20 chronological split."""
        print("\n=== DATA SPLITTING (60/20/20) ===")
        
        n = len(self.df)
        train_size = int(0.6 * n)
        val_size = int(0.2 * n)
        
        self.train_data = self.df.iloc[:train_size].copy()
        self.val_data = self.df.iloc[train_size:train_size + val_size].copy()
        self.test_data = self.df.iloc[train_size + val_size:].copy()
        
        print(f"Training set: {len(self.train_data)} samples ({self.train_data.index[0]} to {self.train_data.index[-1]})")
        print(f"Validation set: {len(self.val_data)} samples ({self.val_data.index[0]} to {self.val_data.index[-1]})")
        print(f"Test set: {len(self.test_data)} samples ({self.test_data.index[0]} to {self.test_data.index[-1]})")
        
    def analyze_acf_pacf(self, data, title=""):
        """Analyze ACF and PACF for parameter selection."""
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        plot_acf(data.dropna(), ax=axes[0], lags=50, title=f'ACF {title}')
        plot_pacf(data.dropna(), ax=axes[1], lags=50, title=f'PACF {title}')
        plt.tight_layout()
        plt.show()
        
    def phase2_initial_training(self):
        """Phase 2: Initial training on 60% with model diagnostics and validation."""
        print("\n=== PHASE 2: INITIAL TRAINING AND VALIDATION ===")
        
        # ACF/PACF analysis for parameter selection
        print("\nAnalyzing ACF/PACF for parameter selection...")
        self.analyze_acf_pacf(self.train_data['Demand'], "- Training Data")
        
        # Manual SARIMAX parameter selection based on ACF/PACF
        # Based on typical electric demand patterns: daily (24h) and weekly (168h) seasonality
        print("\nTraining SARIMAX model...")
        
        # Prepare exogenous variables
        exog_vars = ['Temperature', 'IsHoliday', 'Hour_sin', 'Hour_cos', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']
        train_exog = self.train_data[exog_vars]
        val_exog = self.val_data[exog_vars]
        
        # SARIMAX model with simplified parameters for faster training
        self.sarimax_model = SARIMAX(
            self.train_data['Demand'],
            exog=train_exog,
            order=(1, 1, 1),  # Simplified ARIMA parameters
            seasonal_order=(1, 0, 1, 24),  # Daily seasonality for hourly data
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        
        sarimax_fitted = self.sarimax_model.fit(disp=False)
        print("SARIMAX model fitted successfully")
        
        # Holt-Winter model
        print("\nTraining Holt-Winter model...")
        self.holt_winter_model = ExponentialSmoothing(
            self.train_data['Demand'],
            trend='add',
            seasonal='mul',
            seasonal_periods=24  # Daily seasonality for hourly data
        )
        
        hw_fitted = self.holt_winter_model.fit(optimized=True)
        print("Holt-Winter model fitted successfully")
        
        # Validation predictions
        print("\nGenerating validation predictions...")
        
        # SARIMAX validation predictions
        sarimax_val_pred = sarimax_fitted.forecast(steps=len(self.val_data), exog=val_exog)
        
        # Holt-Winter validation predictions
        hw_val_pred = hw_fitted.forecast(steps=len(self.val_data))
        
        # Calculate MAPE for validation
        sarimax_val_mape = mean_absolute_percentage_error(self.val_data['Demand'], sarimax_val_pred) * 100
        hw_val_mape = mean_absolute_percentage_error(self.val_data['Demand'], hw_val_pred) * 100
        
        print(f"\nValidation Results:")
        print(f"SARIMAX MAPE: {sarimax_val_mape:.2f}%")
        print(f"Holt-Winter MAPE: {hw_val_mape:.2f}%")
        
        # Validation visualization
        plt.figure(figsize=(15, 8))
        plt.plot(self.val_data.index, self.val_data['Demand'], label='Actual', alpha=0.8)
        plt.plot(self.val_data.index, sarimax_val_pred, label='SARIMAX', alpha=0.8)
        plt.plot(self.val_data.index, hw_val_pred, label='Holt-Winter', alpha=0.8)
        plt.title('Validation Set Predictions')
        plt.xlabel('Date')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()
        
        return sarimax_fitted, hw_fitted

    def phase3_final_training_and_testing(self):
        """Phase 3: Retraining on 80% and final validation on 20%."""
        print("\n=== PHASE 3: FINAL TRAINING AND TESTING ===")

        # Combine training and validation data (80%)
        train_val_data = pd.concat([self.train_data, self.val_data])

        print(f"Combined training data: {len(train_val_data)} samples")
        print(f"Test data: {len(self.test_data)} samples")

        # Prepare exogenous variables
        exog_vars = ['Temperature', 'IsHoliday', 'Hour_sin', 'Hour_cos', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']
        train_val_exog = train_val_data[exog_vars]
        test_exog = self.test_data[exog_vars]

        # Retrain SARIMAX on 80% data
        print("\nRetraining SARIMAX model on 80% data...")
        sarimax_final = SARIMAX(
            train_val_data['Demand'],
            exog=train_val_exog,
            order=(1, 1, 1),
            seasonal_order=(1, 0, 1, 24),
            enforce_stationarity=False,
            enforce_invertibility=False
        )
        sarimax_final_fitted = sarimax_final.fit(disp=False)

        # Retrain Holt-Winter on 80% data
        print("Retraining Holt-Winter model on 80% data...")
        hw_final = ExponentialSmoothing(
            train_val_data['Demand'],
            trend='add',
            seasonal='mul',
            seasonal_periods=24
        )
        hw_final_fitted = hw_final.fit(optimized=True)

        # Final test predictions
        print("\nGenerating final test predictions...")

        # SARIMAX test predictions
        sarimax_test_pred = sarimax_final_fitted.forecast(steps=len(self.test_data), exog=test_exog)

        # Holt-Winter test predictions
        hw_test_pred = hw_final_fitted.forecast(steps=len(self.test_data))

        # Calculate final MAPE
        sarimax_test_mape = mean_absolute_percentage_error(self.test_data['Demand'], sarimax_test_pred) * 100
        hw_test_mape = mean_absolute_percentage_error(self.test_data['Demand'], hw_test_pred) * 100

        print(f"\nFinal Test Results:")
        print(f"SARIMAX MAPE: {sarimax_test_mape:.2f}%")
        print(f"Holt-Winter MAPE: {hw_test_mape:.2f}%")

        # Final test visualization
        plt.figure(figsize=(15, 8))
        plt.plot(self.test_data.index, self.test_data['Demand'], label='Actual', alpha=0.8, linewidth=2)
        plt.plot(self.test_data.index, sarimax_test_pred, label='SARIMAX', alpha=0.8, linewidth=2)
        plt.plot(self.test_data.index, hw_test_pred, label='Holt-Winter', alpha=0.8, linewidth=2)
        plt.title('Final Test Set Predictions')
        plt.xlabel('Date')
        plt.ylabel('Demand')
        plt.legend()
        plt.grid(True)
        plt.tight_layout()
        plt.show()

        return sarimax_final_fitted, hw_final_fitted, sarimax_test_pred, hw_test_pred

    def step_ahead_forecasting(self, sarimax_model, hw_model):
        """Generate the 4 specific figures for hourly forecasting."""
        print("\n=== STEP-AHEAD FORECASTING - 4 SPECIFIC FIGURES ===")

        # Prepare data for rolling forecasts
        train_val_data = pd.concat([self.train_data, self.val_data])
        exog_vars = ['Temperature', 'IsHoliday', 'Hour_sin', 'Hour_cos', 'WeekDay_sin', 'WeekDay_cos', 'DayOfYear_sin', 'DayOfYear_cos']

        # Figure 1: ARIMA 24-hour forecasting (t+1 to t+24)
        print("\nGenerating Figure 1: ARIMA 24-hour forecasting...")
        arima_24h_horizons = list(range(1, 25))  # t+1, t+2, ..., t+24
        self.create_multi_horizon_figure(train_val_data, exog_vars, arima_24h_horizons,
                                       "ARIMA", "ARIMA 24-Hour Forecasting: Actual vs Predictions")

        # Figure 2: Holt-Winter 24-hour forecasting (t+1 to t+24)
        print("\nGenerating Figure 2: Holt-Winter 24-hour forecasting...")
        self.create_multi_horizon_figure(train_val_data, exog_vars, arima_24h_horizons,
                                       "Holt-Winter", "Holt-Winter 24-Hour Forecasting: Actual vs Predictions")

        # Figure 3: ARIMA 7-day forecasting (t+24, t+48, ..., t+168)
        print("\nGenerating Figure 3: ARIMA 7-day forecasting...")
        arima_7day_horizons = [24, 48, 72, 96, 120, 144, 168]  # t+24, t+48, t+72, t+96, t+120, t+144, t+168
        self.create_multi_horizon_figure(train_val_data, exog_vars, arima_7day_horizons,
                                       "ARIMA", "ARIMA 7-Day Forecasting: Actual vs Predictions")

        # Figure 4: Holt-Winter 7-day forecasting (t+24, t+48, ..., t+168)
        print("\nGenerating Figure 4: Holt-Winter 7-day forecasting...")
        self.create_multi_horizon_figure(train_val_data, exog_vars, arima_7day_horizons,
                                       "Holt-Winter", "Holt-Winter 7-Day Forecasting: Actual vs Predictions")

    def create_multi_horizon_figure(self, train_data, exog_vars, horizons, model_type, title):
        """Create a single figure showing multiple forecasting horizons."""
        print(f"Creating {title}...")

        # Limit forecasts for efficiency
        max_forecasts = 100

        # Store results for all horizons
        all_predictions = {}
        all_actual = {}
        all_dates = {}

        for horizon in horizons:
            print(f"  Processing horizon t+{horizon}...")

            predictions = []
            actual_values = []
            forecast_dates = []

            # Limit to available data
            n_forecasts = min(max_forecasts, len(self.test_data) - horizon)

            for i in range(0, n_forecasts, 24):  # Refit every 24 steps
                # Current training data
                current_train_data = pd.concat([train_data, self.test_data.iloc[:i]]) if i > 0 else train_data

                try:
                    if model_type == "ARIMA":
                        # SARIMAX model
                        model = SARIMAX(
                            current_train_data['Demand'],
                            exog=current_train_data[exog_vars],
                            order=(1, 1, 1),
                            seasonal_order=(1, 0, 1, 24),
                            enforce_stationarity=False,
                            enforce_invertibility=False
                        )
                        fitted_model = model.fit(disp=False)
                    else:  # Holt-Winter
                        model = ExponentialSmoothing(
                            current_train_data['Demand'],
                            trend='add',
                            seasonal='mul',
                            seasonal_periods=24
                        )
                        fitted_model = model.fit(optimized=True)

                    # Generate forecasts for next 24 steps
                    for j in range(min(24, n_forecasts - i)):
                        forecast_idx = i + j
                        if forecast_idx + horizon >= len(self.test_data):
                            break

                        if model_type == "ARIMA":
                            # Get exogenous data for forecast
                            forecast_exog = self.test_data.iloc[forecast_idx:forecast_idx+horizon+1][exog_vars]
                            pred = fitted_model.forecast(steps=horizon+1, exog=forecast_exog)[-1]
                        else:  # Holt-Winter
                            pred = fitted_model.forecast(steps=horizon+1)[-1]

                        # Actual value
                        actual = self.test_data.iloc[forecast_idx + horizon]['Demand']

                        predictions.append(pred)
                        actual_values.append(actual)
                        forecast_dates.append(self.test_data.index[forecast_idx + horizon])

                except Exception as e:
                    print(f"    Error at step {i}: {e}")
                    continue

            # Store results
            all_predictions[horizon] = predictions
            all_actual[horizon] = actual_values
            all_dates[horizon] = forecast_dates

        # Create the multi-horizon figure
        plt.figure(figsize=(20, 12))

        # Use different colors for each horizon
        colors = plt.cm.tab20(np.linspace(0, 1, len(horizons)))

        # Plot actual values (use first horizon's dates as reference)
        if horizons[0] in all_dates and len(all_dates[horizons[0]]) > 0:
            plt.plot(all_dates[horizons[0]], all_actual[horizons[0]],
                    label='Actual', linewidth=3, color='black', alpha=0.8)

        # Plot predictions for each horizon
        for i, horizon in enumerate(horizons):
            if horizon in all_predictions and len(all_predictions[horizon]) > 0:
                # Calculate MAPE for this horizon
                mape = mean_absolute_percentage_error(all_actual[horizon], all_predictions[horizon]) * 100

                plt.plot(all_dates[horizon], all_predictions[horizon],
                        label=f't+{horizon} (MAPE: {mape:.1f}%)',
                        linewidth=2, color=colors[i], alpha=0.7)

                print(f"  t+{horizon} MAPE: {mape:.2f}%")

        plt.title(title, fontsize=16, fontweight='bold')
        plt.xlabel('Date', fontsize=12)
        plt.ylabel('Demand', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.show()

        print(f"Completed {title}\n")

    def run_complete_analysis(self):
        """Run the complete 3-phase forecasting analysis."""
        print("Starting Electric Demand Forecasting Analysis")
        print("=" * 60)

        # Phase 1: Data Preparation
        self.load_and_prepare_data()
        self.exploratory_data_analysis()
        self.create_train_val_test_split()

        # Phase 2: Initial Training
        sarimax_fitted, hw_fitted = self.phase2_initial_training()

        # Phase 3: Final Training and Testing
        sarimax_final, hw_final, sarimax_pred, hw_pred = self.phase3_final_training_and_testing()

        # Step-ahead forecasting
        self.step_ahead_forecasting(sarimax_final, hw_final)

        print("\n" + "=" * 60)
        print("Analysis completed successfully!")


def main():
    """Main execution function."""
    # Initialize forecaster
    forecaster = ElectricDemandForecaster('electric_demand_1h.csv')

    # Run complete analysis
    forecaster.run_complete_analysis()


if __name__ == "__main__":
    main()
