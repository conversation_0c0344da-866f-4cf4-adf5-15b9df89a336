"""
Advanced Time Series Forecasting with Regression Models
(Linear Regression, Random Forest, LightGBM, XGBoost)

This program implements a comprehensive regression-based approach to time series forecasting
following the advanced methodology with proper feature engineering, cross-validation,
hyperparameter tuning, and visualization.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning imports
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_absolute_percentage_error
import lightgbm as lgb
import xgboost as xgb

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class RegressionTimeSeriesForecaster:
    def __init__(self, data_path):
        """Initialize the forecaster with data path"""
        self.data_path = data_path
        self.data = None
        self.X = None
        self.y = None
        self.forecast_horizons = [1, 8, 16, 24]  # t+1, t+8, t+16, t+24
        self.models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(random_state=42, n_jobs=-1),
            'LightGBM': lgb.LGBMRegressor(random_state=42, verbose=-1),
            'XGBoost': xgb.XGBRegressor(random_state=42, n_jobs=-1)
        }
        self.best_models = {}
        self.results = {}
        
    def load_and_prepare_data(self):
        """Phase 1: Load data and create datetime index"""
        print("Phase 1: Loading and preparing base data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        
        # Create datetime index
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data.set_index('DateTime', inplace=True)
        self.data = self.data.asfreq('H')  # Ensure hourly frequency
        
        print(f"Data loaded: {len(self.data)} records from {self.data.index[0]} to {self.data.index[-1]}")
        print(f"Columns: {list(self.data.columns)}")
        
    def create_time_features(self):
        """Create comprehensive time-based features"""
        print("Creating time-based features...")
        
        # Basic time components
        self.data['hour'] = self.data.index.hour
        self.data['dayofweek'] = self.data.index.dayofweek
        self.data['dayofyear'] = self.data.index.dayofyear
        self.data['month'] = self.data.index.month
        self.data['year'] = self.data.index.year
        self.data['weekofyear'] = self.data.index.isocalendar().week
        
        # Binary flags
        self.data['Is_Working_Day'] = (self.data['dayofweek'] < 5).astype(int)  # Mon-Fri = 1
        
        # Cyclical encoding (Essential for capturing periodic patterns)
        # Hour (24-hour cycle)
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data['hour'] / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data['hour'] / 24)
        
        # Day of week (7-day cycle)
        self.data['day_of_week_sin'] = np.sin(2 * np.pi * self.data['dayofweek'] / 7)
        self.data['day_of_week_cos'] = np.cos(2 * np.pi * self.data['dayofweek'] / 7)
        
        # Month (12-month cycle)
        self.data['month_sin'] = np.sin(2 * np.pi * self.data['month'] / 12)
        self.data['month_cos'] = np.cos(2 * np.pi * self.data['month'] / 12)
        
        print("Time-based features created successfully")
        
    def create_lag_features(self):
        """Create lag features for recent past demand values"""
        print("Creating lag features...")
        
        # Critical lag features
        self.data['Demand_lag_24'] = self.data['Demand'].shift(24)   # Same hour yesterday
        self.data['Demand_lag_48'] = self.data['Demand'].shift(48)   # Same hour 2 days ago
        self.data['Demand_lag_168'] = self.data['Demand'].shift(168) # Same hour last week
        
        print("Lag features created successfully")
        
    def create_rolling_features(self):
        """Create rolling window statistical features"""
        print("Creating rolling window features...")
        
        # 24-hour rolling statistics
        self.data['rolling_mean_24h'] = self.data['Demand'].rolling(window=24, min_periods=1).mean()
        self.data['rolling_std_24h'] = self.data['Demand'].rolling(window=24, min_periods=1).std()
        self.data['rolling_min_24h'] = self.data['Demand'].rolling(window=24, min_periods=1).min()
        self.data['rolling_max_24h'] = self.data['Demand'].rolling(window=24, min_periods=1).max()
        
        print("Rolling window features created successfully")
        
    def prepare_feature_matrix(self):
        """Finalize the feature matrix X and target variable y"""
        print("Preparing feature matrix...")
        
        # Define feature columns (exclude target and original time columns)
        feature_cols = [
            'Temperature', 'Holiday', 'Is_Working_Day',
            'hour', 'dayofweek', 'dayofyear', 'month', 'year', 'weekofyear',
            'hour_sin', 'hour_cos', 'day_of_week_sin', 'day_of_week_cos',
            'month_sin', 'month_cos',
            'Demand_lag_24', 'Demand_lag_48', 'Demand_lag_168',
            'rolling_mean_24h', 'rolling_std_24h', 'rolling_min_24h', 'rolling_max_24h'
        ]
        
        # Create feature matrix
        self.X = self.data[feature_cols].copy()
        self.y = self.data['Demand'].copy()
        
        # Remove rows with NaN values (due to lag and rolling features)
        valid_idx = ~(self.X.isnull().any(axis=1) | self.y.isnull())
        self.X = self.X[valid_idx]
        self.y = self.y[valid_idx]
        
        print(f"Feature matrix prepared: {self.X.shape}")
        print(f"Features: {list(self.X.columns)}")
        
    def split_data(self):
        """Split data chronologically: 60% train, 20% validation, 20% test"""
        print("Splitting data chronologically...")
        
        n = len(self.X)
        train_size = int(0.6 * n)
        val_size = int(0.2 * n)
        
        self.X_train = self.X.iloc[:train_size]
        self.y_train = self.y.iloc[:train_size]
        
        self.X_val = self.X.iloc[train_size:train_size + val_size]
        self.y_val = self.y.iloc[train_size:train_size + val_size]
        
        self.X_test = self.X.iloc[train_size + val_size:]
        self.y_test = self.y.iloc[train_size + val_size:]
        
        # Combined train+val for final training
        self.X_train_val = self.X.iloc[:train_size + val_size]
        self.y_train_val = self.y.iloc[:train_size + val_size]
        
        print(f"Train set: {len(self.X_train)} samples")
        print(f"Validation set: {len(self.X_val)} samples") 
        print(f"Test set: {len(self.X_test)} samples")
        
    def create_target_variables(self):
        """Create target variables for different forecast horizons"""
        print("Creating target variables for different horizons...")

        self.targets = {}
        # Use the original full demand series before feature matrix filtering
        full_demand = self.data['Demand']

        for h in self.forecast_horizons:
            # Create target by shifting demand backward by h steps
            target_name = f'y_{h}'
            # Align with the feature matrix index
            shifted_target = full_demand.shift(-h)
            # Keep only the indices that match our feature matrix
            self.targets[target_name] = shifted_target.loc[self.X.index]
            print(f"Created target {target_name} for horizon t+{h}")
            
    def phase1_feature_engineering(self):
        """Execute complete Phase 1: Feature Engineering"""
        print("="*60)
        print("PHASE 1: ADVANCED FEATURE ENGINEERING")
        print("="*60)
        
        self.load_and_prepare_data()
        self.create_time_features()
        self.create_lag_features()
        self.create_rolling_features()
        self.prepare_feature_matrix()
        self.split_data()
        self.create_target_variables()
        
        print("Phase 1 completed successfully!")
        print("="*60)

    def phase2_cross_validation(self):
        """Phase 2: Robust Model Evaluation with Time Series Cross-Validation"""
        print("="*60)
        print("PHASE 2: ROBUST MODEL EVALUATION WITH CROSS-VALIDATION")
        print("="*60)

        # Initialize TimeSeriesSplit with fewer splits for faster execution
        tscv = TimeSeriesSplit(n_splits=3)
        self.cv_results = {}

        for h in self.forecast_horizons:
            print(f"\nEvaluating models for forecast horizon t+{h}...")

            # Get target for this horizon
            target_name = f'y_{h}'
            y_target = self.targets[target_name]

            # Align target with train_val indices and remove NaN values
            y_target_aligned = y_target.loc[self.X_train_val.index]
            valid_idx = ~y_target_aligned.isnull()
            X_valid = self.X_train_val[valid_idx]
            y_valid = y_target_aligned[valid_idx]

            if len(y_valid) == 0:
                print(f"No valid data for horizon {h}, skipping...")
                continue

            horizon_results = {}

            # Test each algorithm
            for model_name, model in self.models.items():
                print(f"  Testing {model_name}...")

                fold_scores = []

                # Cross-validation loop
                for train_idx, val_idx in tscv.split(X_valid):
                    X_train_fold = X_valid.iloc[train_idx]
                    y_train_fold = y_valid.iloc[train_idx]
                    X_val_fold = X_valid.iloc[val_idx]
                    y_val_fold = y_valid.iloc[val_idx]

                    # Fit model
                    model.fit(X_train_fold, y_train_fold)

                    # Predict
                    y_pred_fold = model.predict(X_val_fold)

                    # Calculate MAE
                    mae = mean_absolute_error(y_val_fold, y_pred_fold)
                    fold_scores.append(mae)

                # Store average MAE across folds
                avg_mae = np.mean(fold_scores)
                horizon_results[model_name] = {
                    'avg_mae': avg_mae,
                    'fold_scores': fold_scores
                }

                print(f"    Average MAE: {avg_mae:.2f}")

            # Find best model for this horizon
            best_model_name = min(horizon_results.keys(),
                                key=lambda x: horizon_results[x]['avg_mae'])

            self.cv_results[f'horizon_{h}'] = {
                'results': horizon_results,
                'best_model': best_model_name,
                'best_mae': horizon_results[best_model_name]['avg_mae']
            }

            print(f"  Best model for t+{h}: {best_model_name} (MAE: {horizon_results[best_model_name]['avg_mae']:.2f})")

        print("\nPhase 2 completed successfully!")
        print("="*60)

    def phase3_hyperparameter_tuning(self):
        """Phase 3: Hyperparameter Tuning for Best Models"""
        print("="*60)
        print("PHASE 3: HYPERPARAMETER TUNING")
        print("="*60)

        # Define simplified parameter grids for faster tuning
        param_grids = {
            'Linear Regression': {},  # No hyperparameters to tune
            'Random Forest': {
                'n_estimators': [100, 300],
                'max_depth': [10, 20]
            },
            'LightGBM': {
                'n_estimators': [100, 300],
                'learning_rate': [0.05, 0.1],
                'max_depth': [3, 5]
            },
            'XGBoost': {
                'n_estimators': [100, 300],
                'learning_rate': [0.05, 0.1],
                'max_depth': [3, 5]
            }
        }

        tscv = TimeSeriesSplit(n_splits=3)

        for h in self.forecast_horizons:
            if f'horizon_{h}' not in self.cv_results:
                continue

            best_model_name = self.cv_results[f'horizon_{h}']['best_model']
            print(f"\nTuning {best_model_name} for horizon t+{h}...")

            # Get target for this horizon
            target_name = f'y_{h}'
            y_target = self.targets[target_name]

            # Align target with train_val indices and remove NaN values
            y_target_aligned = y_target.loc[self.X_train_val.index]
            valid_idx = ~y_target_aligned.isnull()
            X_valid = self.X_train_val[valid_idx]
            y_valid = y_target_aligned[valid_idx]

            if len(y_valid) == 0:
                continue

            # Get base model and parameter grid
            base_model = self.models[best_model_name]
            param_grid = param_grids[best_model_name]

            if not param_grid:  # No parameters to tune (e.g., Linear Regression)
                print(f"  No hyperparameters to tune for {best_model_name}")
                self.best_models[f'horizon_{h}'] = {
                    'model': base_model,
                    'best_params': {},
                    'best_score': self.cv_results[f'horizon_{h}']['best_mae']
                }
                continue

            # Set up GridSearchCV
            grid_search = GridSearchCV(
                estimator=base_model,
                param_grid=param_grid,
                cv=tscv,
                scoring='neg_mean_absolute_error',
                n_jobs=-1,
                verbose=0
            )

            # Fit grid search
            grid_search.fit(X_valid, y_valid)

            # Store best model
            self.best_models[f'horizon_{h}'] = {
                'model': grid_search.best_estimator_,
                'best_params': grid_search.best_params_,
                'best_score': -grid_search.best_score_
            }

            print(f"  Best parameters: {grid_search.best_params_}")
            print(f"  Best MAE: {-grid_search.best_score_:.2f}")

        print("\nPhase 3 completed successfully!")
        print("="*60)

    def phase4_final_evaluation(self):
        """Phase 4: Final Evaluation and Visualization"""
        print("="*60)
        print("PHASE 4: FINAL EVALUATION AND VISUALIZATION")
        print("="*60)

        self.final_predictions = {}
        self.final_metrics = {}

        for h in self.forecast_horizons:
            if f'horizon_{h}' not in self.best_models:
                continue

            print(f"\nFinal evaluation for horizon t+{h}...")

            # Get the best tuned model
            best_model = self.best_models[f'horizon_{h}']['model']

            # Get target for this horizon
            target_name = f'y_{h}'
            y_target = self.targets[target_name]

            # Prepare test data (align and remove NaN values)
            y_target_test = y_target.loc[self.X_test.index]
            test_valid_idx = ~y_target_test.isnull()
            X_test_valid = self.X_test[test_valid_idx]
            y_test_valid = y_target_test[test_valid_idx]

            if len(y_test_valid) == 0:
                print(f"  No valid test data for horizon {h}")
                continue

            # Train final model on combined train+validation data
            y_target_train_val = y_target.loc[self.X_train_val.index]
            train_val_valid_idx = ~y_target_train_val.isnull()
            X_train_val_valid = self.X_train_val[train_val_valid_idx]
            y_train_val_valid = y_target_train_val[train_val_valid_idx]

            best_model.fit(X_train_val_valid, y_train_val_valid)

            # Generate predictions on test set
            y_pred = best_model.predict(X_test_valid)

            # Calculate metrics
            mae = mean_absolute_error(y_test_valid, y_pred)
            mape = mean_absolute_percentage_error(y_test_valid, y_pred) * 100

            # Store results
            self.final_predictions[f'horizon_{h}'] = {
                'y_true': y_test_valid,
                'y_pred': y_pred,
                'timestamps': y_test_valid.index
            }

            self.final_metrics[f'horizon_{h}'] = {
                'MAE': mae,
                'MAPE': mape,
                'model_name': self.cv_results[f'horizon_{h}']['best_model']
            }

            print(f"  Model: {self.final_metrics[f'horizon_{h}']['model_name']}")
            print(f"  MAE: {mae:.2f}")
            print(f"  MAPE: {mape:.2f}%")

        print("\nPhase 4 completed successfully!")
        print("="*60)

    def create_visualizations(self):
        """Create comprehensive visualizations"""
        print("Creating visualizations...")

        # Create a figure with subplots for each horizon
        fig, axes = plt.subplots(2, 2, figsize=(20, 15))
        axes = axes.flatten()

        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728']

        for i, h in enumerate(self.forecast_horizons):
            if f'horizon_{h}' not in self.final_predictions:
                continue

            ax = axes[i]

            # Get prediction data
            pred_data = self.final_predictions[f'horizon_{h}']
            y_true = pred_data['y_true']
            y_pred = pred_data['y_pred']
            timestamps = pred_data['timestamps']

            # Plot only a subset for clarity (last 500 points)
            n_points = min(500, len(y_true))
            idx_subset = slice(-n_points, None)

            ax.plot(timestamps[idx_subset], y_true.iloc[idx_subset],
                   label='Actual', color='black', linewidth=1.5, alpha=0.8)
            ax.plot(timestamps[idx_subset], y_pred[idx_subset],
                   label='Predicted', color=colors[i], linewidth=1.5, alpha=0.8)

            # Get metrics
            metrics = self.final_metrics[f'horizon_{h}']
            model_name = metrics['model_name']
            mae = metrics['MAE']
            mape = metrics['MAPE']

            ax.set_title(f'Forecast Performance at t+{h}\n{model_name} - MAE: {mae:.2f}, MAPE: {mape:.2f}%',
                        fontsize=12, fontweight='bold')
            ax.set_xlabel('Time', fontsize=10)
            ax.set_ylabel('Demand', fontsize=10)
            ax.legend(fontsize=10)
            ax.grid(True, alpha=0.3)

            # Rotate x-axis labels for better readability
            ax.tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig('regression_forecasting_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Create summary table
        self.create_summary_table()

    def create_summary_table(self):
        """Create a summary table of results"""
        print("\n" + "="*80)
        print("FINAL RESULTS SUMMARY")
        print("="*80)

        summary_data = []
        for h in self.forecast_horizons:
            if f'horizon_{h}' in self.final_metrics:
                metrics = self.final_metrics[f'horizon_{h}']
                summary_data.append({
                    'Horizon': f't+{h}',
                    'Best Model': metrics['model_name'],
                    'MAE': f"{metrics['MAE']:.2f}",
                    'MAPE (%)': f"{metrics['MAPE']:.2f}"
                })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))

        print("="*80)

    def run_complete_analysis(self):
        """Run the complete analysis pipeline"""
        print("Starting Advanced Regression-based Time Series Forecasting Analysis...")
        print("="*80)

        try:
            # Execute all phases
            self.phase1_feature_engineering()
            self.phase2_cross_validation()
            self.phase3_hyperparameter_tuning()
            self.phase4_final_evaluation()
            self.create_visualizations()

            print("\n" + "="*80)
            print("ANALYSIS COMPLETED SUCCESSFULLY!")
            print("="*80)

        except Exception as e:
            print(f"Error during analysis: {str(e)}")
            raise


def main():
    """Main execution function"""
    print("Advanced Time Series Forecasting with Regression Models")
    print("=" * 80)
    print("Algorithms: Linear Regression, Random Forest, LightGBM, XGBoost")
    print("Methodology: Feature Engineering + Cross-Validation + Hyperparameter Tuning")
    print("=" * 80)

    # Initialize forecaster
    forecaster = RegressionTimeSeriesForecaster('electric_demand_1h.csv')

    # Run complete analysis
    forecaster.run_complete_analysis()

    return forecaster


if __name__ == "__main__":
    # Execute the analysis
    forecaster = main()
